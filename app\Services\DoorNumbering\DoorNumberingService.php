<?php

namespace App\Services\DoorNumbering;

use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Door Numbering Service
 * 
 * Main service for automatic door number calculation based on the hierarchical structure.
 * Supports Types 1, 2, and 3 numbering schemes as documented in the door numbering system.
 */
class DoorNumberingService
{
    /**
     * Calculate door numbers for a Korpus based on its numbering type
     *
     * @param Korpus $korpus
     * @param bool $regenerateExisting
     * @return array
     */
    public function calculateKorpusDoorNumbers(Korpus $korpus, bool $regenerateExisting = false): array
    {
        try {
            DB::beginTransaction();

            $numberingType = $korpus->numbering_type ?? 1;
            
            Log::info('DoorNumberingService: Starting door number calculation', [
                'korpus_id' => $korpus->id,
                'numbering_type' => $numberingType,
                'regenerate_existing' => $regenerateExisting
            ]);

            $result = match ($numberingType) {
                1 => $this->calculateType1Numbers($korpus, $regenerateExisting),
                2 => $this->calculateType2Numbers($korpus, $regenerateExisting),
                3 => $this->calculateType3Numbers($korpus, $regenerateExisting),
                default => ['success' => false, 'error' => 'Unsupported numbering type: ' . $numberingType]
            };

            if ($result['success']) {
                DB::commit();
                Log::info('DoorNumberingService: Door number calculation completed successfully', [
                    'korpus_id' => $korpus->id,
                    'result' => $result
                ]);
            } else {
                DB::rollBack();
                Log::error('DoorNumberingService: Door number calculation failed', [
                    'korpus_id' => $korpus->id,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('DoorNumberingService: Exception during door number calculation', [
                'korpus_id' => $korpus->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Calculate Type 1 door numbers: Korpus-wise consecutive numbering
     */
    protected function calculateType1Numbers(Korpus $korpus, bool $regenerateExisting): array
    {
        $orcs = $korpus->orcs()->orderBy('number')->get();
        
        if ($orcs->isEmpty()) {
            return ['success' => false, 'error' => 'No Orcs found for this Korpus'];
        }

        $beginNumber = $korpus->begin_toot_number ?? 1;
        $endNumber = $korpus->end_toot_number ?? 100;
        $totalDoors = $endNumber - $beginNumber + 1;
        $doorsPerOrc = intval($totalDoors / $orcs->count());
        
        $currentDoorNumber = $beginNumber;
        $generatedDoors = 0;

        foreach ($orcs as $orc) {
            $davhars = $orc->davhars()->orderBy('order')->get();
            
            if ($davhars->isEmpty()) {
                continue;
            }

            $doorsPerDavhar = intval($doorsPerOrc / $davhars->count());
            
            foreach ($davhars as $davhar) {
                $startDoor = $currentDoorNumber;
                $endDoor = $currentDoorNumber + $doorsPerDavhar - 1;
                
                // Update Davhar door range
                $davhar->update([
                    'begin_toot_number' => $startDoor,
                    'end_toot_number' => $endDoor
                ]);

                // Generate or update Toot records
                $this->generateTootsForDavhar($davhar, $startDoor, $endDoor, $regenerateExisting);
                
                $currentDoorNumber += $doorsPerDavhar;
                $generatedDoors += $doorsPerDavhar;
            }
        }

        return [
            'success' => true,
            'type' => 1,
            'doors_generated' => $generatedDoors,
            'range' => "{$beginNumber}-{$endNumber}"
        ];
    }

    /**
     * Calculate Type 2 door numbers: Orc-wise consecutive numbering
     */
    protected function calculateType2Numbers(Korpus $korpus, bool $regenerateExisting): array
    {
        $orcs = $korpus->orcs()->orderBy('number')->get();
        
        if ($orcs->isEmpty()) {
            return ['success' => false, 'error' => 'No Orcs found for this Korpus'];
        }

        $totalGeneratedDoors = 0;

        foreach ($orcs as $orc) {
            $davhars = $orc->davhars()->orderBy('order')->get();
            
            if ($davhars->isEmpty()) {
                continue;
            }

            $doorsPerOrc = $orc->end_toot_number ?? 48; // Default doors per Orc
            $doorsPerDavhar = intval($doorsPerOrc / $davhars->count());
            $currentDoorNumber = 1; // Start from 1 for each Orc

            foreach ($davhars as $davhar) {
                $startDoor = $currentDoorNumber;
                $endDoor = $currentDoorNumber + $doorsPerDavhar - 1;
                
                // Update Davhar door range
                $davhar->update([
                    'begin_toot_number' => $startDoor,
                    'end_toot_number' => $endDoor
                ]);

                // Generate or update Toot records
                $this->generateTootsForDavhar($davhar, $startDoor, $endDoor, $regenerateExisting);
                
                $currentDoorNumber += $doorsPerDavhar;
                $totalGeneratedDoors += $doorsPerDavhar;
            }
        }

        return [
            'success' => true,
            'type' => 2,
            'doors_generated' => $totalGeneratedDoors
        ];
    }

    /**
     * Calculate Type 3 door numbers: Floor-wise consecutive numbering
     */
    protected function calculateType3Numbers(Korpus $korpus, bool $regenerateExisting): array
    {
        $orcs = $korpus->orcs()->orderBy('number')->get();
        
        if ($orcs->isEmpty()) {
            return ['success' => false, 'error' => 'No Orcs found for this Korpus'];
        }

        $digitMultiplier = $korpus->digit_multiplier ?? 100;
        $totalGeneratedDoors = 0;

        foreach ($orcs as $orc) {
            $davhars = $orc->davhars()->orderBy('order')->get();
            
            if ($davhars->isEmpty()) {
                continue;
            }

            foreach ($davhars as $index => $davhar) {
                $floorNumber = $davhar->floor_number ?? ($index + 1);
                $doorsPerFloor = 6; // Default doors per floor
                
                // Type 3 calculation: (floor_number * digit_multiplier) + door_sequence
                $startDoor = ($floorNumber * $digitMultiplier) + 1;
                $endDoor = ($floorNumber * $digitMultiplier) + $doorsPerFloor;
                
                // Update Davhar with floor number and door range
                $davhar->update([
                    'floor_number' => $floorNumber,
                    'begin_toot_number' => $startDoor,
                    'end_toot_number' => $endDoor
                ]);

                // Generate or update Toot records
                $this->generateTootsForDavhar($davhar, $startDoor, $endDoor, $regenerateExisting);
                
                $totalGeneratedDoors += $doorsPerFloor;
            }
        }

        return [
            'success' => true,
            'type' => 3,
            'doors_generated' => $totalGeneratedDoors,
            'digit_multiplier' => $digitMultiplier
        ];
    }

    /**
     * Generate Toot records for a Davhar within the specified door range
     */
    protected function generateTootsForDavhar(Davhar $davhar, int $startDoor, int $endDoor, bool $regenerateExisting): void
    {
        if ($regenerateExisting) {
            // Delete existing Toots for this Davhar
            Toot::where('davhar_id', $davhar->id)->delete();
        }

        for ($doorNumber = $startDoor; $doorNumber <= $endDoor; $doorNumber++) {
            // Check if Toot already exists
            $existingToot = Toot::where('davhar_id', $davhar->id)
                ->where('number', $doorNumber)
                ->first();

            if (!$existingToot) {
                Toot::create([
                    'korpus_id' => $davhar->orc->korpus_id,
                    'davhar_id' => $davhar->id,
                    'number' => $doorNumber
                ]);
            }
        }
    }

    /**
     * Validate door numbering configuration for a Korpus
     */
    public function validateConfiguration(Korpus $korpus): array
    {
        $errors = [];
        
        $numberingType = $korpus->numbering_type ?? 1;
        
        switch ($numberingType) {
            case 1:
                if (!$korpus->begin_toot_number || !$korpus->end_toot_number) {
                    $errors[] = 'Type 1 requires begin_toot_number and end_toot_number';
                }
                if ($korpus->begin_toot_number >= $korpus->end_toot_number) {
                    $errors[] = 'begin_toot_number must be less than end_toot_number';
                }
                break;
                
            case 3:
                if (!$korpus->digit_multiplier) {
                    $errors[] = 'Type 3 requires digit_multiplier';
                }
                if (!in_array($korpus->digit_multiplier, [10, 100, 1000])) {
                    $errors[] = 'digit_multiplier must be 10, 100, or 1000';
                }
                break;
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Get door numbering statistics for a Korpus
     */
    public function getStatistics(Korpus $korpus): array
    {
        $orcs = $korpus->orcs()->with('davhars.toots')->get();
        $totalOrcs = $orcs->count();
        $totalDavhars = $orcs->sum(fn($orc) => $orc->davhars->count());
        $totalToots = $orcs->sum(fn($orc) => $orc->davhars->sum(fn($davhar) => $davhar->toots->count()));
        
        return [
            'korpus_id' => $korpus->id,
            'numbering_type' => $korpus->numbering_type ?? 1,
            'total_orcs' => $totalOrcs,
            'total_davhars' => $totalDavhars,
            'total_toots' => $totalToots,
            'digit_multiplier' => $korpus->digit_multiplier,
            'door_range' => $korpus->begin_toot_number && $korpus->end_toot_number 
                ? "{$korpus->begin_toot_number}-{$korpus->end_toot_number}" 
                : null
        ];
    }
}
