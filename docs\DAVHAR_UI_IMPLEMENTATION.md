# Davhar UI Implementation Documentation

## Overview

This document describes the implementation of UI resources for the Davhar (floor) entity following the established hierarchical structure and patterns used by other entities in the codebase.

## Hierarchical Structure

The Davhar entity is positioned between Orcs (entrances) and Toots (doors) in the hierarchy:

```
Su<PERSON> (Residents' Committee)
└── Bair (Building)
    └── Korpus (Block)
        └── Orc (Entrance)
            └── Davhar (Floor) [NEW UI]
                └── Toot (Door)
```

## Implemented Components

### 1. DavharResource (`app/Filament/Resources/Admin/DavharResource.php`)

**Features:**
- Full CRUD operations for Davhar entities
- Hierarchical form with Orc selection showing full path (Bair - Korpus - Orc)
- Form validation with unique constraints within Orc
- Door numbering range fields (begin_toot_number, end_toot_number)
- Floor number field for Type 3 door numbering
- CVSecurity code display (read-only)
- Mongolian language labels consistent with existing UI

**Table Columns:**
- Row index
- Building name (Bair)
- Block name (Korpus)
- Entrance number (Orc)
- Floor number (Davhar)
- Order
- Door range (begin-end)
- CVSecurity code

**Navigation:**
- Group: "Барилга" (Building)
- Sort order: 4
- Icon: heroicon-o-building-office

### 2. OrcResource (`app/Filament/Resources/Admin/OrcResource.php`)

**Features:**
- Full CRUD operations for Orc entities
- Hierarchical form with Korpus selection showing full path (Bair - Korpus)
- Form validation with unique constraints within Korpus
- Door numbering range fields
- CVSecurity code display (read-only)
- Davhars count column in table
- Relation manager for managing Davhars

**Navigation:**
- Group: "Барилга" (Building)
- Sort order: 3
- Icon: heroicon-o-arrow-right-on-rectangle

### 3. DavharsRelationManager (`app/Filament/Resources/Admin/OrcResource/RelationManagers/DavharsRelationManager.php`)

**Features:**
- Manage Davhars within an Orc context
- Inline creation and editing
- Automatic parent relationship assignment
- Bulk operations with proper cleanup
- Door range display
- Toots count column

### 4. TootsRelationManager (`app/Filament/Resources/Admin/DavharResource/RelationManagers/TootsRelationManager.php`)

**Features:**
- Manage Toots within a Davhar context
- Manual Toot creation
- Automatic Toot generation based on door range
- Confirmation dialog for bulk generation
- Proper parent relationship assignment (both davhar_id and korpus_id)

### 5. UserInfoService Extensions (`app/Services/UserInfoService.php`)

**New Methods:**
- `getAUOrcsFromKorpus($korpusId)`: Get Orcs for a specific Korpus
- `getAUDavhars($orcId)`: Get Davhars for a specific Orc

## Page Components

### DavharResource Pages
- `ListDavhars.php`: List all Davhars with filtering by user's Sukh
- `CreateDavhar.php`: Create new Davhar
- `EditDavhar.php`: Edit existing Davhar

### OrcResource Pages
- `ListOrcs.php`: List all Orcs with filtering by user's Sukh
- `CreateOrc.php`: Create new Orc
- `EditOrc.php`: Edit existing Orc

## Integration Features

### CVSecurity Integration
- Automatic synchronization through existing DavharObserver
- CVSecurity code display in UI (read-only)
- Proper error handling for service unavailability

### Door Numbering System
- Support for door numbering Types 1, 2, and 3
- Floor number field for Type 3 numbering
- Door range validation and display
- Automatic Toot generation based on ranges

### Hierarchical Navigation
- Consistent parent-child relationship management
- Proper filtering based on user's Sukh
- Breadcrumb-style display in selection fields

## UI Patterns and Consistency

### Form Patterns
- Section-based layouts
- Grid columns for responsive design
- Live validation
- Helper text for complex fields
- Consistent field labeling in Mongolian

### Table Patterns
- Row indexing
- Hierarchical column display
- Sortable and searchable columns
- Consistent action buttons
- Bulk operations

### Navigation Patterns
- Grouped navigation (Барилга group)
- Consistent icons and sorting
- Mongolian labels

## Security and Access Control

### Data Filtering
- All queries filtered by user's Sukh
- Proper relationship-based filtering
- Consistent access patterns across resources

### Validation
- Unique constraints within parent entities
- Range validation for door numbers
- Required field validation

## Testing Considerations

The implementation follows existing patterns and should be tested with:

1. **CRUD Operations**: Create, read, update, delete for both Davhars and Orcs
2. **Hierarchical Relationships**: Proper parent-child associations
3. **CVSecurity Integration**: Automatic sync operations
4. **Door Numbering**: Range validation and Toot generation
5. **Access Control**: Proper filtering by user's Sukh
6. **UI Responsiveness**: Form layouts and table displays

## Future Enhancements

1. **Door Numbering Preview**: Integration with existing preview components
2. **Bulk Operations**: Enhanced bulk creation and management
3. **Import/Export**: CSV import/export functionality
4. **Advanced Filtering**: Additional table filters
5. **Reporting**: Floor and door utilization reports

## Notes

- All UI components follow existing codebase patterns
- Mongolian language labels are consistent with other resources
- CVSecurity integration is handled automatically through observers
- Door numbering system integration is ready for Types 1, 2, and 3
- Resources are automatically discovered by Filament AdminPanelProvider
