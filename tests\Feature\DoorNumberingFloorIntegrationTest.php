<?php

namespace Tests\Feature;

use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use App\Services\DoorNumbering\DoorNumberingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DoorNumberingFloorIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected DoorNumberingService $doorNumberingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->doorNumberingService = app(DoorNumberingService::class);
    }

    public function test_floor_entity_integration_with_type_1_numbering()
    {
        // Create test data
        $bair = Bair::factory()->create();
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 24,
            'numbering_type' => 1,
            'digit_multiplier' => 100
        ]);

        $orc = Orc::create([
            'korpus_id' => $korpus->id,
            'number' => '1',
            'begin_toot_number' => 1,
            'end_toot_number' => 24
        ]);

        // Create floors (Davhars)
        $floor1 = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '1',
            'order' => 1
        ]);

        $floor2 = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '2',
            'order' => 2
        ]);

        // Generate door numbers
        $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus);

        // Assert success
        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['type']);

        // Verify floor door ranges were calculated
        $floor1->refresh();
        $floor2->refresh();

        $this->assertEquals(1, $floor1->begin_toot_number);
        $this->assertEquals(12, $floor1->end_toot_number);
        $this->assertEquals(13, $floor2->begin_toot_number);
        $this->assertEquals(24, $floor2->end_toot_number);

        // Verify doors were created
        $this->assertEquals(12, Toot::where('davhar_id', $floor1->id)->count());
        $this->assertEquals(12, Toot::where('davhar_id', $floor2->id)->count());
    }

    public function test_floor_entity_integration_with_type_3_numbering()
    {
        // Create test data
        $bair = Bair::factory()->create();
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 3,
            'digit_multiplier' => 100
        ]);

        $orc = Orc::create([
            'korpus_id' => $korpus->id,
            'number' => '1'
        ]);

        // Create floors (Davhars)
        $floor1 = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '1',
            'order' => 1,
            'floor_number' => 1
        ]);

        $floor2 = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '2',
            'order' => 2,
            'floor_number' => 2
        ]);

        // Generate door numbers
        $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus);

        // Assert success
        $this->assertTrue($result['success']);
        $this->assertEquals(3, $result['type']);

        // Verify floor door ranges were calculated (Type 3: floor_number * digit_multiplier + sequence)
        $floor1->refresh();
        $floor2->refresh();

        $this->assertEquals(101, $floor1->begin_toot_number); // 1 * 100 + 1
        $this->assertEquals(106, $floor1->end_toot_number);   // 1 * 100 + 6
        $this->assertEquals(201, $floor2->begin_toot_number); // 2 * 100 + 1
        $this->assertEquals(206, $floor2->end_toot_number);   // 2 * 100 + 6

        // Verify doors were created with correct numbers
        $floor1Doors = Toot::where('davhar_id', $floor1->id)->orderBy('number')->pluck('number')->toArray();
        $floor2Doors = Toot::where('davhar_id', $floor2->id)->orderBy('number')->pluck('number')->toArray();

        $this->assertEquals([101, 102, 103, 104, 105, 106], $floor1Doors);
        $this->assertEquals([201, 202, 203, 204, 205, 206], $floor2Doors);
    }

    public function test_hierarchical_structure_korpus_to_floor_to_door()
    {
        // Create test data
        $bair = Bair::factory()->create();
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 2
        ]);

        $orc = Orc::create([
            'korpus_id' => $korpus->id,
            'number' => '1',
            'end_toot_number' => 12
        ]);

        $floor = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '1',
            'order' => 1
        ]);

        // Generate door numbers
        $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus);

        // Verify hierarchical relationships
        $this->assertTrue($result['success']);
        
        // Test Korpus → Davhars relationship
        $korpusDavhars = $korpus->davhars;
        $this->assertCount(1, $korpusDavhars);
        $this->assertEquals($floor->id, $korpusDavhars->first()->id);

        // Test Orc → Davhars relationship
        $orcDavhars = $orc->davhars;
        $this->assertCount(1, $orcDavhars);
        $this->assertEquals($floor->id, $orcDavhars->first()->id);

        // Test Davhar → Toots relationship
        $floor->refresh();
        $floorToots = $floor->toots;
        $this->assertCount(12, $floorToots);

        // Verify all doors belong to the correct Korpus and Davhar
        foreach ($floorToots as $toot) {
            $this->assertEquals($korpus->id, $toot->korpus_id);
            $this->assertEquals($floor->id, $toot->davhar_id);
        }
    }

    public function test_door_numbering_statistics_include_floors()
    {
        // Create test data
        $bair = Bair::factory()->create();
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'numbering_type' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 24
        ]);

        $orc = Orc::create([
            'korpus_id' => $korpus->id,
            'number' => '1'
        ]);

        // Create 2 floors
        Davhar::create(['orc_id' => $orc->id, 'number' => '1', 'order' => 1]);
        Davhar::create(['orc_id' => $orc->id, 'number' => '2', 'order' => 2]);

        // Generate door numbers
        $this->doorNumberingService->calculateKorpusDoorNumbers($korpus);

        // Get statistics
        $stats = $this->doorNumberingService->getStatistics($korpus);

        // Verify statistics include floors
        $this->assertEquals(1, $stats['total_orcs']);
        $this->assertEquals(2, $stats['total_davhars']); // 2 floors
        $this->assertEquals(24, $stats['total_toots']);  // 24 doors total
        $this->assertEquals('1-24', $stats['door_range']);
    }
}
