<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\DavharResource\Pages;
use App\Filament\Resources\Admin\DavharResource\RelationManagers;
use App\Models\Davhar;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Unique;

class DavharResource extends Resource
{
    protected static ?string $model = Davhar::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?string $navigationLabel = 'Давхар';
    protected static ?string $pluralModelLabel = 'Давхрууд';
    protected static ?string $modelLabel = 'Давхар';
    protected static ?int $navigationSort = 4;
    protected static ?string $slug = 'davhars';
    protected static ?string $navigationGroup = 'Барилга';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make(Davhar::ORC_ID)
                            ->label('Орц')
                            ->options(function () {
                                $service = resolve(UserInfoService::class);
                                $sukh = $service->getAUSukh();
                                $options = [];
                                foreach ($sukh->bairs as $bair) {
                                    foreach ($bair->korpuses as $korpus) {
                                        foreach ($korpus->orcs as $orc) {
                                            $options[$orc->id] = "{$bair->name} - {$korpus->name} - Орц {$orc->number}";
                                        }
                                    }
                                }
                                return $options;
                            })
                            ->searchable()
                            ->required(),

                        Forms\Components\TextInput::make(Davhar::NUMBER)
                            ->label('Давхрын дугаар')
                            ->required()
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule, callable $get) {
                                    $orcId = $get(Davhar::ORC_ID);
                                    return $rule->where('orc_id', $orcId);
                                }
                            ),

                        Forms\Components\TextInput::make(Davhar::ORDER)
                            ->label('Дараалал')
                            ->numeric()
                            ->required()
                            ->default(1)
                            ->minValue(1),

                        Forms\Components\TextInput::make(Davhar::BEGIN_TOOT_NUMBER)
                            ->label('Тоот эхлэх дугаар')
                            ->numeric()
                            ->required()
                            ->default(1)
                            ->minValue(1)
                            ->live(),

                        Forms\Components\TextInput::make(Davhar::END_TOOT_NUMBER)
                            ->label('Тоот дуусах дугаар')
                            ->numeric()
                            ->required()
                            ->minValue(fn ($get) => (int) $get(Davhar::BEGIN_TOOT_NUMBER) + 1)
                            ->live(),

                        Forms\Components\TextInput::make(Davhar::FLOOR_NUMBER)
                            ->label('Давхрын дугаар (Type 3)')
                            ->numeric()
                            ->helperText('Type 3 door numbering-д ашиглагдана'),

                        Forms\Components\TextInput::make(Davhar::CODE)
                            ->label('CVSecurity код')
                            ->disabled()
                            ->helperText('CVSecurity системээс автоматаар үүсгэгдэнэ')
                            ->hidden(fn (?Davhar $record) => $record === null),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('orc.korpus.bair.name')
                    ->label('Байр')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('orc.korpus.name')
                    ->label('Блок')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('orc.number')
                    ->label('Орц')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make(Davhar::NUMBER)
                    ->label('Давхар')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make(Davhar::ORDER)
                    ->label('Дараалал')
                    ->sortable(),
                Tables\Columns\TextColumn::make('door_range')
                    ->label('Тоотын хүрээ')
                    ->getStateUsing(fn (Davhar $record): string =>
                        "{$record->begin_toot_number}-{$record->end_toot_number}")
                    ->sortable(),
                Tables\Columns\TextColumn::make(Davhar::CODE)
                    ->label('CVSecurity код')
                    ->formatStateUsing(fn (string $state): string => $state ?: 'Байхгүй')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\TootsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDavhars::route('/'),
            'create' => Pages\CreateDavhar::route('/create'),
            'edit' => Pages\EditDavhar::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('orc.korpus.bair', function (Builder $query) use($sukh) {
            $query->where('sukh_id', $sukh->id);
        });
    }
}
