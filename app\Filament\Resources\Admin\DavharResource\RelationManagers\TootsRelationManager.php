<?php

namespace App\Filament\Resources\Admin\DavharResource\RelationManagers;

use App\Models\Toot;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;

class TootsRelationManager extends RelationManager
{
    protected static string $relationship = 'toots';
    protected static ?string $title = 'Тоотууд';
    protected static ?string $modelLabel = 'Тоот';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make(Toot::NUMBER)
                    ->label('Тоотын дугаар')
                    ->numeric()
                    ->required()
                    ->unique(
                        ignoreRecord: true,
                        modifyRuleUsing: function (Unique $rule) {
                            $davharId = $this->getOwnerRecord()->id;
                            return $rule->where('davhar_id', $davharId);
                        }
                    ),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Toot::NUMBER)
                    ->label('Тоотын дугаар')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Үүсгэсэн огноо')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Тоот нэмэх')
                    ->icon('heroicon-s-plus')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['davhar_id'] = $this->getOwnerRecord()->id;
                        $data['korpus_id'] = $this->getOwnerRecord()->orc->korpus_id;
                        return $data;
                    }),
                Tables\Actions\Action::make('generate_toots')
                    ->label('Тоот автоматаар үүсгэх')
                    ->icon('heroicon-s-cog')
                    ->action(function () {
                        $davhar = $this->getOwnerRecord();
                        $beginTootNumber = $davhar->begin_toot_number;
                        $endTootNumber = $davhar->end_toot_number;

                        // Delete existing toots for this davhar
                        Toot::where('davhar_id', $davhar->id)->delete();

                        // Generate new toots
                        for ($number = $beginTootNumber; $number <= $endTootNumber; $number++) {
                            Toot::create([
                                'davhar_id' => $davhar->id,
                                'korpus_id' => $davhar->orc->korpus_id,
                                'number' => $number,
                            ]);
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Тоот автоматаар үүсгэх')
                    ->modalDescription('Энэ үйлдэл нь одоо байгаа бүх тоотыг устгаад шинээр үүсгэнэ. Та итгэлтэй байна уу?')
                    ->modalSubmitActionLabel('Тийм')
                    ->modalCancelActionLabel('Үгүй'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
