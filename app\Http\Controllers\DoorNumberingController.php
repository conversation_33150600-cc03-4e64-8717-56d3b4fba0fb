<?php

namespace App\Http\Controllers;

use App\Models\Korpus;
use App\Services\DoorNumbering\DoorNumberingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

/**
 * Door Numbering Controller
 * 
 * RESTful API endpoints for door numbering operations
 */
class DoorNumberingController extends Controller
{
    protected DoorNumberingService $doorNumberingService;

    public function __construct(DoorNumberingService $doorNumberingService)
    {
        $this->doorNumberingService = $doorNumberingService;
    }

    /**
     * Generate door numbers for a Korpus
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function generate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'korpus_id' => 'required|integer|exists:korpuses,id',
            'regenerate_existing' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $korpus = Korpus::findOrFail($request->korpus_id);
        $regenerateExisting = $request->boolean('regenerate_existing', false);

        $result = $this->doorNumberingService->calculateKorpusDoorNumbers($korpus, $regenerateExisting);

        return response()->json($result);
    }

    /**
     * Validate door numbering configuration for a Korpus
     * 
     * @param int $korpusId
     * @return JsonResponse
     */
    public function validate(int $korpusId): JsonResponse
    {
        $korpus = Korpus::findOrFail($korpusId);
        $result = $this->doorNumberingService->validateConfiguration($korpus);

        return response()->json($result);
    }

    /**
     * Get door numbering statistics for a Korpus
     * 
     * @param int $korpusId
     * @return JsonResponse
     */
    public function statistics(int $korpusId): JsonResponse
    {
        $korpus = Korpus::findOrFail($korpusId);
        $result = $this->doorNumberingService->getStatistics($korpus);

        return response()->json($result);
    }
}
