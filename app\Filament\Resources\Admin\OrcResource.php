<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\OrcResource\Pages;
use App\Filament\Resources\Admin\OrcResource\RelationManagers;
use App\Models\Orc;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Unique;

class OrcResource extends Resource
{
    protected static ?string $model = Orc::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-right-on-rectangle';
    protected static ?string $navigationLabel = 'Орц';
    protected static ?string $pluralModelLabel = 'Орцууд';
    protected static ?string $modelLabel = 'Орц';
    protected static ?int $navigationSort = 3;
    protected static ?string $slug = 'orcs';
    protected static ?string $navigationGroup = 'Барилга';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make(Orc::KORPUS_ID)
                            ->label('Блок')
                            ->options(function () {
                                $service = resolve(UserInfoService::class);
                                $sukh = $service->getAUSukh();
                                $options = [];
                                foreach ($sukh->bairs as $bair) {
                                    foreach ($bair->korpuses as $korpus) {
                                        $options[$korpus->id] = "{$bair->name} - {$korpus->name}";
                                    }
                                }
                                return $options;
                            })
                            ->searchable()
                            ->required(),

                        Forms\Components\TextInput::make(Orc::NUMBER)
                            ->label('Орцны дугаар')
                            ->numeric()
                            ->required()
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule, callable $get) {
                                    $korpusId = $get(Orc::KORPUS_ID);
                                    return $rule->where('korpus_id', $korpusId);
                                }
                            ),

                        Forms\Components\TextInput::make(Orc::BEGIN_TOOT_NUMBER)
                            ->label('Тоот эхлэх дугаар')
                            ->numeric()
                            ->required()
                            ->default(1)
                            ->minValue(1)
                            ->live(),

                        Forms\Components\TextInput::make(Orc::END_TOOT_NUMBER)
                            ->label('Тоот дуусах дугаар')
                            ->numeric()
                            ->required()
                            ->minValue(fn ($get) => (int) $get(Orc::BEGIN_TOOT_NUMBER) + 1)
                            ->live(),

                        Forms\Components\TextInput::make(Orc::CODE)
                            ->label('CVSecurity код')
                            ->disabled()
                            ->helperText('CVSecurity системээс автоматаар үүсгэгдэнэ')
                            ->hidden(fn (?Orc $record) => $record === null),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('korpus.bair.name')
                    ->label('Байр')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('korpus.name')
                    ->label('Блок')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make(Orc::NUMBER)
                    ->label('Орцны дугаар')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('door_range')
                    ->label('Тоотын хүрээ')
                    ->getStateUsing(fn (Orc $record): string => 
                        "{$record->begin_toot_number}-{$record->end_toot_number}")
                    ->sortable(),
                Tables\Columns\TextColumn::make('davhars_count')
                    ->label('Давхрын тоо')
                    ->counts('davhars')
                    ->sortable(),
                Tables\Columns\TextColumn::make(Orc::CODE)
                    ->label('CVSecurity код')
                    ->formatStateUsing(fn (string $state): string => $state ?: 'Байхгүй')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\DavharsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrcs::route('/'),
            'create' => Pages\CreateOrc::route('/create'),
            'edit' => Pages\EditOrc::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('korpus.bair', function (Builder $query) use($sukh) {
            $query->where('sukh_id', $sukh->id);
        });
    }
}
