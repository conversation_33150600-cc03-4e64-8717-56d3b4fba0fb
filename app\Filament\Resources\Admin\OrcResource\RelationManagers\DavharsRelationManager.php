<?php

namespace App\Filament\Resources\Admin\OrcResource\RelationManagers;

use App\Models\Davhar;
use App\Models\Toot;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rules\Unique;

class DavharsRelationManager extends RelationManager
{
    protected static string $relationship = 'davhars';
    protected static ?string $title = 'Давхрууд';
    protected static ?string $modelLabel = 'Давхар';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make(Davhar::NUMBER)
                            ->label('Давхрын дугаар')
                            ->required()
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule) {
                                    $orcId = $this->getOwnerRecord()->id;
                                    return $rule->where('orc_id', $orcId);
                                }
                            ),
                        Forms\Components\TextInput::make(Davhar::ORDER)
                            ->label('Дараалал')
                            ->numeric()
                            ->required()
                            ->default(1)
                            ->minValue(1),
                        Forms\Components\TextInput::make(Davhar::BEGIN_TOOT_NUMBER)
                            ->label('Тоот эхлэх дугаар')
                            ->numeric()
                            ->required()
                            ->default(1)
                            ->minValue(1)
                            ->live(),
                        Forms\Components\TextInput::make(Davhar::END_TOOT_NUMBER)
                            ->label('Тоот дуусах дугаар')
                            ->numeric()
                            ->required()
                            ->minValue(fn ($get) => (int) $get(Davhar::BEGIN_TOOT_NUMBER) + 1)
                            ->live(),
                        Forms\Components\TextInput::make(Davhar::FLOOR_NUMBER)
                            ->label('Давхрын дугаар (Type 3)')
                            ->numeric()
                            ->helperText('Type 3 door numbering-д ашиглагдана'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Davhar::NUMBER)
                    ->label('Давхрын дугаар')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make(Davhar::ORDER)
                    ->label('Дараалал')
                    ->sortable(),
                Tables\Columns\TextColumn::make('door_range')
                    ->label('Тоотын хүрээ')
                    ->getStateUsing(fn (Davhar $record): string => 
                        "{$record->begin_toot_number}-{$record->end_toot_number}")
                    ->sortable(),
                Tables\Columns\TextColumn::make('toots_count')
                    ->label('Тоотын тоо')
                    ->counts('toots')
                    ->sortable(),
                Tables\Columns\TextColumn::make(Davhar::CODE)
                    ->label('CVSecurity код')
                    ->formatStateUsing(fn (?string $state): string => $state ?: 'Байхгүй')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Давхар нэмэх')
                    ->icon('heroicon-s-plus')
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['orc_id'] = $this->getOwnerRecord()->id;
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->after(function (Model $record) {
                        // Delete associated toots when davhar is deleted
                        Toot::where('davhar_id', $record->id)->delete();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->after(function ($records) {
                            // Delete associated toots when davhars are bulk deleted
                            $davharIds = $records->pluck('id')->toArray();
                            Toot::whereIn('davhar_id', $davharIds)->delete();
                        }),
                ]),
            ]);
    }
}
