<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('davhars', function (Blueprint $table) {
            $table->integer('floor_number')->nullable()->comment('Floor identifier for Type 3 numbering (1, 2, 3, ...)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('davhars', function (Blueprint $table) {
            $table->dropColumn('floor_number');
        });
    }
};
