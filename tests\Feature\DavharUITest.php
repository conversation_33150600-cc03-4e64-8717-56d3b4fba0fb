<?php

namespace Tests\Feature;

use App\Models\Sukh;
use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DavharUITest extends TestCase
{
    use RefreshDatabase;

    protected $sukh;
    protected $bair;
    protected $korpus;
    protected $orc;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data hierarchy
        $this->sukh = Sukh::factory()->create();
        $this->bair = Bair::factory()->create(['sukh_id' => $this->sukh->id]);
        $this->korpus = Korpus::factory()->create(['bair_id' => $this->bair->id]);
        $this->orc = Orc::factory()->create(['korpus_id' => $this->korpus->id]);
    }

    /** @test */
    public function davhar_can_be_created_with_proper_relationships()
    {
        $davharData = [
            'orc_id' => $this->orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
            'floor_number' => 4,
        ];

        $davhar = Davhar::create($davharData);

        $this->assertDatabaseHas('davhars', [
            'orc_id' => $this->orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
            'floor_number' => 4,
        ]);

        // Test relationships
        $this->assertEquals($this->orc->id, $davhar->orc->id);
        $this->assertEquals($this->korpus->id, $davhar->orc->korpus->id);
        $this->assertEquals($this->bair->id, $davhar->orc->korpus->bair->id);
        $this->assertEquals($this->sukh->id, $davhar->orc->korpus->bair->sukh->id);
    }

    /** @test */
    public function davhar_enforces_unique_order_within_orc()
    {
        // Create first davhar
        Davhar::create([
            'orc_id' => $this->orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ]);

        // Attempt to create duplicate order should fail
        $this->expectException(\Illuminate\Database\QueryException::class);

        Davhar::create([
            'orc_id' => $this->orc->id,
            'number' => '5', // Different number but same order
            'order' => 1, // Same order in same orc should fail
            'begin_toot_number' => 7,
            'end_toot_number' => 12,
        ]);
    }

    /** @test */
    public function orc_davhars_relationship_works()
    {
        $davhar1 = Davhar::factory()->create(['orc_id' => $this->orc->id, 'order' => 1]);
        $davhar2 = Davhar::factory()->create(['orc_id' => $this->orc->id, 'order' => 2]);

        $this->orc->refresh();
        $davhars = $this->orc->davhars;

        $this->assertCount(2, $davhars);
        $this->assertEquals($davhar1->id, $davhars->first()->id);
        $this->assertEquals($davhar2->id, $davhars->last()->id);
    }

    /** @test */
    public function davhar_toots_relationship_works()
    {
        $davhar = Davhar::factory()->create(['orc_id' => $this->orc->id]);

        // Create toots for this davhar
        $toot1 = \App\Models\Toot::create([
            'davhar_id' => $davhar->id,
            'korpus_id' => $this->korpus->id,
            'number' => 101,
        ]);

        $toot2 = \App\Models\Toot::create([
            'davhar_id' => $davhar->id,
            'korpus_id' => $this->korpus->id,
            'number' => 102,
        ]);

        $davhar->refresh();
        $toots = $davhar->toots;

        $this->assertCount(2, $toots);
        $this->assertEquals($toot1->id, $toots->first()->id);
        $this->assertEquals($toot2->id, $toots->last()->id);
    }

    /** @test */
    public function hierarchical_structure_is_maintained()
    {
        $davhar = Davhar::factory()->create(['orc_id' => $this->orc->id]);

        // Test full hierarchy: Sukh → Bair → Korpus → Orc → Davhar
        $this->assertEquals($this->sukh->id, $davhar->orc->korpus->bair->sukh->id);
        $this->assertEquals($this->bair->id, $davhar->orc->korpus->bair->id);
        $this->assertEquals($this->korpus->id, $davhar->orc->korpus->id);
        $this->assertEquals($this->orc->id, $davhar->orc->id);
    }
}
