<?php

namespace Tests\Feature;

use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DoorNumberingApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user for authentication
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');
    }

    public function test_generate_door_numbers_api_endpoint()
    {
        // Create test data
        $bair = Bair::factory()->create();
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 12,
            'numbering_type' => 1
        ]);

        $orc = Orc::create([
            'korpus_id' => $korpus->id,
            'number' => '1'
        ]);

        Davhar::create([
            'orc_id' => $orc->id,
            'number' => '1',
            'order' => 1
        ]);

        // Test the API endpoint
        $response = $this->postJson('/api/door-numbering/generate', [
            'korpus_id' => $korpus->id,
            'regenerate_existing' => false
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'type' => 1
                 ]);
    }

    public function test_validate_configuration_api_endpoint()
    {
        // Create test data
        $bair = Bair::factory()->create();
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 12,
            'numbering_type' => 1
        ]);

        // Test the API endpoint
        $response = $this->getJson("/api/door-numbering/validate/{$korpus->id}");

        $response->assertStatus(200)
                 ->assertJson([
                     'valid' => true,
                     'errors' => []
                 ]);
    }

    public function test_statistics_api_endpoint()
    {
        // Create test data
        $bair = Bair::factory()->create();
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => 'Test Korpus',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 12,
            'numbering_type' => 1
        ]);

        $orc = Orc::create([
            'korpus_id' => $korpus->id,
            'number' => '1'
        ]);

        Davhar::create([
            'orc_id' => $orc->id,
            'number' => '1',
            'order' => 1
        ]);

        // Test the API endpoint
        $response = $this->getJson("/api/door-numbering/statistics/{$korpus->id}");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'korpus_id',
                     'numbering_type',
                     'total_orcs',
                     'total_davhars',
                     'total_toots',
                     'door_range'
                 ]);
    }

    public function test_generate_door_numbers_validation_error()
    {
        // Test with invalid data
        $response = $this->postJson('/api/door-numbering/generate', [
            'korpus_id' => 999999, // Non-existent korpus
            'regenerate_existing' => false
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['korpus_id']);
    }

    public function test_validate_configuration_with_invalid_korpus()
    {
        // Test with non-existent korpus
        $response = $this->getJson('/api/door-numbering/validate/999999');

        $response->assertStatus(404);
    }
}
